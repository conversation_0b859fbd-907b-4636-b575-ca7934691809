#!/bin/bash

echo "=== gRPC分布式日志记录系统演示 ==="
echo ""

echo "1. 编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo ""
echo "2. 启动gRPC日志服务器..."
echo "   服务器将在后台运行，监听端口9090"
mvn exec:java -Dexec.mainClass="com.example.grpc.server.LogServer" &
SERVER_PID=$!

# 等待服务器启动
sleep 5

echo ""
echo "3. 启动多个客户端进行演示..."
echo "   客户端将每秒发送一次日志到服务器"

# 启动第一个客户端
echo "   启动 client-A..."
mvn exec:java -Dexec.mainClass="com.example.grpc.client.LogClient" -Dexec.args="client-A" &
CLIENT_A_PID=$!

sleep 2

# 启动第二个客户端
echo "   启动 client-B..."
mvn exec:java -Dexec.mainClass="com.example.grpc.client.LogClient" -Dexec.args="client-B" &
CLIENT_B_PID=$!

echo ""
echo "4. 演示运行中..."
echo "   观察服务端控制台输出，可以看到："
echo "   - 多个客户端的日志被异步处理"
echo "   - 日志格式: [级别] 客户端名称 时间戳 - 消息内容"
echo "   - 3个消费者线程并发处理日志队列"
echo ""
echo "   按 Ctrl+C 停止演示"

# 等待用户中断
trap 'echo ""; echo "正在停止演示..."; kill $SERVER_PID $CLIENT_A_PID $CLIENT_B_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
while true; do
    sleep 1
done
