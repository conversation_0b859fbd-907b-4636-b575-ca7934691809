<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="V_ZRR_TEMP_TABLE_HJ">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="area" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="idcard" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="xm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="dh" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="flag" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="VZrrTempTableHj" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="epac_log_flow">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="opernateName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="opernateCode" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="opernateTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="apiName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="opernateResult" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="returnResult" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="ipStr" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext">
                      <map>
                        <entry key="jdbcType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                        <entry key="sqlType">
                          <value>
                            <Object />
                          </value>
                        </entry>
                      </map>
                    </option>
                    <option name="name" value="param" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="EpacLogFlow" />
              <option name="preName" value="" />
              <option name="saveModelName" value="epac" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="./src/main/java/com/fiberhome/epac" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="v_zrr_temp_table_hj">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="area" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="idcard" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="xm" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="dh" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="flag" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="VZrrTempTableHj" />
              <option name="preName" value="" />
              <option name="saveModelName" value="" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="" />
              <option name="templateGroupName" value="" />
            </TableInfoDTO>
          </value>
        </entry>
        <entry key="ym_hit_one_nohit_two_latlag_2220">
          <value>
            <TableInfoDTO>
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="idcardNo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="personName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="teleNo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="delFlag" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="injectDate" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="streetName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="communityName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="fulladdr" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="updateTime" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="comment" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="lastAddress" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="isOther" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="age" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymAddrOne" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymAddrTwo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymZcTypeOne" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymZcTypeTwo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymKindOne" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymKindTwo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="lastUpdateTime" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="injectDateTwo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="organNameOne" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="organNameTwo" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="userName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymAddrThree" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymZcTypeThree" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="ymKindThree" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="injectDateThree" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="organNameThree" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="dataAddTime" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="mobilePopulation" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="conductingPropaganda" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="personHealthStatus" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="suitableInoculationMethod" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="name" value="cydd" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="经度" />
                    <option name="custom" value="false" />
                    <option name="name" value="lat" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="纬度" />
                    <option name="custom" value="false" />
                    <option name="name" value="lng" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="YmHitOneNohitTwoLatlag2220" />
              <option name="preName" value="" />
              <option name="saveModelName" value="fszs-authcenter" />
              <option name="savePackageName" value="" />
              <option name="savePath" value="./疫情防控/fszs/fszs-openapi/src/main/java/entity" />
              <option name="templateGroupName" value="Default" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>