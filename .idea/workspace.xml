<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="06c66e99-91fd-4cb6-a293-6197fae233a7" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GotoClassSymbolConfiguration">
    <file-type-list>
      <filtered-out-file-type name="$XSLT" />
      <filtered-out-file-type name="AiIgnore" />
      <filtered-out-file-type name="GitIgnore" />
      <filtered-out-file-type name="HgIgnore" />
      <filtered-out-file-type name="IgnoreLang" />
      <filtered-out-file-type name="ECMA Script Level 4" />
      <filtered-out-file-type name="Redshift" />
      <filtered-out-file-type name="AOPTarget" />
      <filtered-out-file-type name="CassandraQL" />
      <filtered-out-file-type name="Derby" />
      <filtered-out-file-type name="HiveQL" />
      <filtered-out-file-type name="SparkSQL" />
      <filtered-out-file-type name="AZURE" />
      <filtered-out-file-type name="BigQuery" />
      <filtered-out-file-type name="ClickHouse" />
      <filtered-out-file-type name="Cockroach" />
      <filtered-out-file-type name="Cookie" />
      <filtered-out-file-type name="CouchbaseQuery" />
      <filtered-out-file-type name="CronExp" />
      <filtered-out-file-type name="CSS" />
      <filtered-out-file-type name="DTD" />
      <filtered-out-file-type name="Dynamo" />
      <filtered-out-file-type name="EQL" />
      <filtered-out-file-type name="ECMAScript 6" />
      <filtered-out-file-type name="EditorConfig" />
      <filtered-out-file-type name="EJBQL" />
      <filtered-out-file-type name="EL" />
      <filtered-out-file-type name="Exasol" />
      <filtered-out-file-type name="GitExclude" />
      <filtered-out-file-type name="Flow JS" />
      <filtered-out-file-type name="FTL" />
      <filtered-out-file-type name="GenericSQL" />
      <filtered-out-file-type name="GithubExpressionLanguage" />
      <filtered-out-file-type name="Declarative" />
      <filtered-out-file-type name="Greenplum" />
      <filtered-out-file-type name="Groovy" />
      <filtered-out-file-type name="H2" />
      <filtered-out-file-type name="HQL" />
      <filtered-out-file-type name="HSQLDB" />
      <filtered-out-file-type name="HTML" />
      <filtered-out-file-type name="HtmlCompatible" />
      <filtered-out-file-type name="HTTP Request" />
      <filtered-out-file-type name="DB2_IS" />
      <filtered-out-file-type name="DB2" />
      <filtered-out-file-type name="DB2_ZOS" />
      <filtered-out-file-type name="InjectedFreeMarker" />
      <filtered-out-file-type name="JakartaDataQL" />
      <filtered-out-file-type name="JavaScript" />
      <filtered-out-file-type name="JPAQL" />
      <filtered-out-file-type name="JQL" />
      <filtered-out-file-type name="JQuery-CSS" />
      <filtered-out-file-type name="JShellLanguage" />
      <filtered-out-file-type name="JSON" />
      <filtered-out-file-type name="JSON Lines" />
      <filtered-out-file-type name="JSON5" />
      <filtered-out-file-type name="JSONPath" />
      <filtered-out-file-type name="JSP" />
      <filtered-out-file-type name="JSPX" />
      <filtered-out-file-type name="JSRegexp" />
      <filtered-out-file-type name="JSUnicodeRegexp" />
      <filtered-out-file-type name="JVM" />
      <filtered-out-file-type name="UAST" />
      <filtered-out-file-type name="kotlin" />
      <filtered-out-file-type name="KND" />
      <filtered-out-file-type name="LESS" />
      <filtered-out-file-type name="Lombok.Config" />
      <filtered-out-file-type name="Manifest" />
      <filtered-out-file-type name="MariaDB" />
      <filtered-out-file-type name="Markdown" />
      <filtered-out-file-type name="Micronaut-MongoDB-JSON" />
      <filtered-out-file-type name="MicronautDataQL" />
      <filtered-out-file-type name="MicronautEL" />
      <filtered-out-file-type name="TSQL" />
      <filtered-out-file-type name="MongoDB-JSON" />
      <filtered-out-file-type name="MySQL" />
      <filtered-out-file-type name="MySQL based" />
      <filtered-out-file-type name="Oracle" />
      <filtered-out-file-type name="NetSuite" />
      <filtered-out-file-type name="OracleSqlPlus" />
      <filtered-out-file-type name="TEXT" />
      <filtered-out-file-type name="PointcutExpression" />
      <filtered-out-file-type name="PostCSS" />
      <filtered-out-file-type name="PostgreSQL" />
      <filtered-out-file-type name="Properties" />
      <filtered-out-file-type name="protobase" />
      <filtered-out-file-type name="protobuf" />
      <filtered-out-file-type name="prototext" />
      <filtered-out-file-type name="Qute" />
      <filtered-out-file-type name="Redis" />
      <filtered-out-file-type name="RegExp" />
      <filtered-out-file-type name="RELAX-NG" />
      <filtered-out-file-type name="SASS" />
      <filtered-out-file-type name="SCSS" />
      <filtered-out-file-type name="Shell Script" />
      <filtered-out-file-type name="Snowflake" />
      <filtered-out-file-type name="SPI" />
      <filtered-out-file-type name="Spring-MongoDB-JSON" />
      <filtered-out-file-type name="SpringDataQL" />
      <filtered-out-file-type name="SpEL" />
      <filtered-out-file-type name="SQL" />
      <filtered-out-file-type name="SQL92" />
      <filtered-out-file-type name="SQLite" />
      <filtered-out-file-type name="SVG" />
      <filtered-out-file-type name="Sybase" />
      <filtered-out-file-type name="textmate" />
      <filtered-out-file-type name="ThymeleafExpressions" />
      <filtered-out-file-type name="ThymeleafIterateExpressions" />
      <filtered-out-file-type name="ThymeleafSpringSecurityExtras" />
      <filtered-out-file-type name="ThymeleafTemplatesExpressions" />
      <filtered-out-file-type name="ThymeleafTemplatesFragmentExpressions" />
      <filtered-out-file-type name="ThymeleafUrlExpressions" />
      <filtered-out-file-type name="TOML" />
      <filtered-out-file-type name="TypeScript" />
      <filtered-out-file-type name="TypeScript JSX" />
      <filtered-out-file-type name="UastContextLanguage" />
      <filtered-out-file-type name="Vertica" />
      <filtered-out-file-type name="VTL" />
      <filtered-out-file-type name="XHTML" />
      <filtered-out-file-type name="XML" />
      <filtered-out-file-type name="XPath" />
      <filtered-out-file-type name="XPath2" />
      <filtered-out-file-type name="XsdRegExp" />
      <filtered-out-file-type name="yaml" />
    </file-type-list>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="/opt/homebrew/Cellar/maven/3.9.9" />
        <option name="localRepository" value="$USER_HOME$/soft/maven" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="/opt/homebrew/Cellar/maven/3.9.9/libexec/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="30iotfDAVYNLMV6v4L9BQCshhkn" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.LogServer.executor": "Run",
    "Application.MultiClientLauncher.executor": "Run",
    "Application.StressTestClient.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Desktop/东莞小程序/技能考核/2025年JavaWeb中级编程题考试_A/JavaWeb中级_X0001/JavaWeb中级_exam4",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "MavenSettings"
  }
}]]></component>
  <component name="RunManager" selected="Application.StressTestClient">
    <configuration name="LogServer" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.grpc.server.LogServer" />
      <module name="grpc-log-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.grpc.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MultiClientLauncher" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.grpc.client.MultiClientLauncher" />
      <module name="grpc-log-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.grpc.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StressTestClient" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.grpc.client.StressTestClient" />
      <module name="grpc-log-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.grpc.client.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.StressTestClient" />
        <item itemvalue="Application.MultiClientLauncher" />
        <item itemvalue="Application.LogServer" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="06c66e99-91fd-4cb6-a293-6197fae233a7" name="Changes" comment="" />
      <created>1754119969806</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754119969806</updated>
      <workItem from="1754119970950" duration="2102000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>