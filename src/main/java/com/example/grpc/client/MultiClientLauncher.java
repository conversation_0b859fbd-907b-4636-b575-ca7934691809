package com.example.grpc.client;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 多客户端启动器
 */
public class MultiClientLauncher {
    
    public static void main(String[] args) {
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        // 启动多个客户端
        String[] clientNames = {"client-A", "client-B", "client-C"};
        
        for (String clientName : clientNames) {
            executor.submit(() -> {
                try {
                    LogClient client = new LogClient("localhost", 9090, clientName);
                    
                    // 添加关闭钩子
                    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                        try {
                            client.shutdown();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }));
                    
                    client.startSendingLogs();
                    
                    // 保持客户端运行
                    Thread.currentThread().join();
                    
                } catch (Exception e) {
                    System.err.println("客户端 " + clientName + " 启动失败: " + e.getMessage());
                }
            });
        }
        
        System.out.println("已启动多个客户端: " + String.join(", ", clientNames));
    }
}
