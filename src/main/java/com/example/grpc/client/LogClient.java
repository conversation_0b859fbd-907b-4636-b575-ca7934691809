package com.example.grpc.client;

import com.example.grpc.proto.LogBean;
import com.example.grpc.proto.LogLevel;
import com.example.grpc.proto.LogResponse;
import com.example.grpc.proto.LogServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * gRPC日志客户端
 */
public class LogClient {
    private final ManagedChannel channel;
    private final LogServiceGrpc.LogServiceBlockingStub blockingStub;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final String clientName;
    private final Random random = new Random();

    // 模拟日志消息
    private final String[] infoMessages = {
            "应用启动成功。",
            "用户登录成功。",
            "数据同步完成。",
            "缓存刷新完成。"
    };

    private final String[] warnMessages = {
            "配置文件缺失，使用默认值。",
            "内存使用率较高。",
            "连接池接近满载。",
            "响应时间较慢。"
    };

    private final String[] errorMessages = {
            "数据库连接失败：TimeoutException",
            "网络请求超时：SocketTimeoutException",
            "文件读取失败：FileNotFoundException",
            "认证失败：AuthenticationException"
    };

    public LogClient(String host, int port, String clientName) {
        this.clientName = clientName;
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = LogServiceGrpc.newBlockingStub(channel);
    }

    /**
     * 开始定时发送日志
     */
    public void startSendingLogs() {
        System.out.println("客户端 " + clientName + " 开始定时发送日志...");
        
        // 每秒发送一次日志
        scheduler.scheduleAtFixedRate(this::sendRandomLog, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 发送随机日志
     */
    private void sendRandomLog() {
        try {
            LogBean logBean = generateRandomLog();
            LogResponse response = blockingStub.sendLog(logBean);
            
            if (response.getSuccess()) {
                System.out.println("客户端 " + clientName + " 成功发送日志: " + 
                        logBean.getLevel().name() + " - " + logBean.getMessage());
            } else {
                // 模拟实现第6点要求，记录错误日志
                System.err.println("客户端 " + clientName + " 发送日志失败: " + 
                        response.getErrorMessage());
            }
            
        } catch (StatusRuntimeException e) {
            System.err.println("客户端 " + clientName + " RPC调用失败: " + e.getStatus());
        } catch (Exception e) {
            System.err.println("客户端 " + clientName + " 发送日志时发生错误: " + e.getMessage());
        }
    }

    /**
     * 生成随机日志
     */
    private LogBean generateRandomLog() {
        LogLevel[] levels = {LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.DEBUG};
        LogLevel level = levels[random.nextInt(levels.length)];
        
        String message;
        switch (level) {
            case INFO:
                message = infoMessages[random.nextInt(infoMessages.length)];
                break;
            case WARN:
                message = warnMessages[random.nextInt(warnMessages.length)];
                break;
            case ERROR:
                message = errorMessages[random.nextInt(errorMessages.length)];
                break;
            case DEBUG:
                message = "调试信息: 变量值检查完成。";
                break;
            default:
                message = "未知日志消息";
        }

        return LogBean.newBuilder()
                .setLevel(level)
                .setTimestamp(System.currentTimeMillis())
                .setMessage(message)
                .setClientName(clientName)
                .build();
    }

    /**
     * 关闭客户端
     */
    public void shutdown() throws InterruptedException {
        scheduler.shutdown();
        channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    public static void main(String[] args) throws InterruptedException {
        // 可以启动多个客户端实例
        String clientName = args.length > 0 ? args[0] : "client-A";
        
        LogClient client = new LogClient("localhost", 9090, clientName);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                client.shutdown();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }));
        
        client.startSendingLogs();
        
        // 保持客户端运行
        Thread.currentThread().join();
    }
}
