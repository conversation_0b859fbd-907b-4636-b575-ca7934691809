package com.example.grpc.client;

import com.example.grpc.proto.LogBean;
import com.example.grpc.proto.LogLevel;
import com.example.grpc.proto.LogResponse;
import com.example.grpc.proto.LogServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 压力测试客户端 - 用于测试队列满时的错误处理
 */
public class StressTestClient {
    private final ManagedChannel channel;
    private final LogServiceGrpc.LogServiceBlockingStub blockingStub;
    private final String clientName;

    public StressTestClient(String host, int port, String clientName) {
        this.clientName = clientName;
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = LogServiceGrpc.newBlockingStub(channel);
    }

    /**
     * 快速发送大量日志以测试队列满的情况
     */
    public void sendLogsRapidly(int count) {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        for (int i = 0; i < count; i++) {
            final int logIndex = i;
            executor.submit(() -> {
                try {
                    LogBean logBean = LogBean.newBuilder()
                            .setLevel(LogLevel.INFO)
                            .setTimestamp(System.currentTimeMillis())
                            .setMessage("压力测试日志 #" + logIndex)
                            .setClientName(clientName)
                            .build();
                    
                    LogResponse response = blockingStub.sendLog(logBean);
                    
                    if (response.getSuccess()) {
                        System.out.println("成功发送日志 #" + logIndex);
                    } else {
                        System.err.println("发送日志 #" + logIndex + " 失败: " + response.getErrorMessage());
                    }
                    
                } catch (StatusRuntimeException e) {
                    System.err.println("RPC调用失败 #" + logIndex + ": " + e.getStatus());
                } catch (Exception e) {
                    System.err.println("发送日志 #" + logIndex + " 时发生错误: " + e.getMessage());
                }
            });
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public void shutdown() throws InterruptedException {
        channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    public static void main(String[] args) throws InterruptedException {
        String clientName = args.length > 0 ? args[0] : "stress-client";
        int logCount = args.length > 1 ? Integer.parseInt(args[1]) : 3000;
        
        StressTestClient client = new StressTestClient("localhost", 9090, clientName);
        
        System.out.println("开始压力测试，发送 " + logCount + " 条日志...");
        client.sendLogsRapidly(logCount);
        
        client.shutdown();
        System.out.println("压力测试完成");
    }
}
