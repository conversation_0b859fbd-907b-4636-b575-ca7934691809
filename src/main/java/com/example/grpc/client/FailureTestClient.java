package com.example.grpc.client;

import com.example.grpc.proto.LogBean;
import com.example.grpc.proto.LogLevel;
import com.example.grpc.proto.LogResponse;
import com.example.grpc.proto.LogServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;

/**
 * 失败场景测试客户端 - 专门测试各种错误处理
 */
public class FailureTestClient {
    private final ManagedChannel channel;
    private final LogServiceGrpc.LogServiceBlockingStub blockingStub;
    private final String clientName;

    public FailureTestClient(String host, int port, String clientName) {
        this.clientName = clientName;
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = LogServiceGrpc.newBlockingStub(channel);
    }

    /**
     * 测试服务器不可用的错误处理
     */
    public void testServerUnavailable() {
        System.out.println("=== 测试服务器不可用的错误处理 ===");
        
        for (int i = 1; i <= 3; i++) {
            System.out.println("\n尝试发送第 " + i + " 次日志...");
            sendTestLog("测试服务器不可用 #" + i);
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 发送测试日志并处理各种错误
     */
    private void sendTestLog(String message) {
        try {
            LogBean logBean = LogBean.newBuilder()
                    .setLevel(LogLevel.ERROR)
                    .setTimestamp(System.currentTimeMillis())
                    .setMessage(message)
                    .setClientName(clientName)
                    .build();
            
            System.out.println("正在发送日志: " + message);
            LogResponse response = blockingStub.sendLog(logBean);
            
            if (response.getSuccess()) {
                System.out.println("✅ 成功发送日志");
            } else {
                // 业务逻辑错误（如队列满）
                System.err.println("❌ 业务逻辑错误: " + response.getErrorMessage());
            }
            
        } catch (StatusRuntimeException e) {
            // gRPC通信错误 - 这是我们要演示的重点
            System.err.println("🔥 gRPC通信失败!");
            System.err.println("   状态码: " + e.getStatus().getCode());
            System.err.println("   错误描述: " + e.getStatus().getDescription());
            System.err.println("   完整状态: " + e.getStatus());
            
            // 根据不同的错误类型给出不同的处理建议
            switch (e.getStatus().getCode()) {
                case UNAVAILABLE:
                    System.err.println("   💡 建议: 服务器可能未启动或网络不可达，请检查服务器状态");
                    break;
                case DEADLINE_EXCEEDED:
                    System.err.println("   💡 建议: 请求超时，可能是网络延迟或服务器负载过高");
                    break;
                case CANCELLED:
                    System.err.println("   💡 建议: 请求被取消，可能是客户端主动取消或网络中断");
                    break;
                default:
                    System.err.println("   💡 建议: 其他gRPC错误，请检查网络连接和服务器状态");
            }
            
        } catch (Exception e) {
            // 其他异常
            System.err.println("💥 其他异常: " + e.getClass().getSimpleName());
            System.err.println("   错误消息: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void shutdown() throws InterruptedException {
        channel.shutdown().awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS);
    }

    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== gRPC客户端错误处理演示 ===");
        System.out.println("此测试将连接到不存在的服务器端口，演示错误处理机制\n");
        
        // 连接到一个不存在的端口（假设9999端口没有服务）
        FailureTestClient client = new FailureTestClient("localhost", 9999, "failure-test-client");
        
        try {
            client.testServerUnavailable();
        } finally {
            client.shutdown();
        }
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("如您所见，客户端能够优雅地处理服务器不可用的情况，");
        System.out.println("记录详细的错误信息，并给出相应的处理建议。");
    }
}
