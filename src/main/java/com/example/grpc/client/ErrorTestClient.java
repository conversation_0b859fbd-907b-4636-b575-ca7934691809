package com.example.grpc.client;

import com.example.grpc.proto.LogBean;
import com.example.grpc.proto.LogLevel;
import com.example.grpc.proto.LogResponse;
import com.example.grpc.proto.LogServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 错误处理测试客户端 - 演示各种失败场景的错误处理
 */
public class ErrorTestClient {
    private final ManagedChannel channel;
    private final LogServiceGrpc.LogServiceBlockingStub blockingStub;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final String clientName;

    public ErrorTestClient(String host, int port, String clientName) {
        this.clientName = clientName;
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
        this.blockingStub = LogServiceGrpc.newBlockingStub(channel);
    }

    /**
     * 测试1：正常发送日志
     */
    public void testNormalSend() {
        System.out.println("\n=== 测试1：正常发送日志 ===");
        sendTestLog("正常日志消息");
    }

    /**
     * 测试2：服务器不可用时的错误处理
     */
    public void testServerUnavailable() {
        System.out.println("\n=== 测试2：服务器不可用错误处理 ===");
        System.out.println("请先停止服务器，然后观察客户端的错误处理...");
        
        // 尝试发送几次日志
        for (int i = 1; i <= 3; i++) {
            System.out.println("尝试发送第 " + i + " 次...");
            sendTestLog("测试服务器不可用 #" + i);
            try {
                Thread.sleep(2000); // 等待2秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 测试3：快速发送大量日志（可能触发队列满）
     */
    public void testQueueFull() {
        System.out.println("\n=== 测试3：队列满错误处理 ===");
        System.out.println("快速发送大量日志，观察队列满时的错误处理...");
        
        for (int i = 1; i <= 100; i++) {
            sendTestLog("快速发送测试 #" + i);
            // 快速发送，不等待
        }
    }

    /**
     * 发送测试日志
     */
    private void sendTestLog(String message) {
        try {
            LogBean logBean = LogBean.newBuilder()
                    .setLevel(LogLevel.INFO)
                    .setTimestamp(System.currentTimeMillis())
                    .setMessage(message)
                    .setClientName(clientName)
                    .build();
            
            LogResponse response = blockingStub.sendLog(logBean);
            
            if (response.getSuccess()) {
                System.out.println("✅ 客户端 " + clientName + " 成功发送日志: " + message);
            } else {
                // 业务逻辑错误（如队列满）
                System.err.println("❌ 客户端 " + clientName + " 发送日志失败: " + 
                        response.getErrorMessage());
            }
            
        } catch (StatusRuntimeException e) {
            // gRPC通信错误
            System.err.println("🔥 客户端 " + clientName + " RPC调用失败: " + e.getStatus());
            System.err.println("   错误详情: " + e.getMessage());
        } catch (Exception e) {
            // 其他异常
            System.err.println("💥 客户端 " + clientName + " 发送日志时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 启动定时发送（演示持续的错误处理）
     */
    public void startContinuousErrorTest() {
        System.out.println("\n=== 启动持续错误测试 ===");
        System.out.println("每2秒发送一次日志，观察各种错误情况的处理...");
        
        scheduler.scheduleAtFixedRate(() -> {
            sendTestLog("持续测试日志 - " + System.currentTimeMillis());
        }, 0, 2, TimeUnit.SECONDS);
    }

    public void shutdown() throws InterruptedException {
        scheduler.shutdown();
        channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    public static void main(String[] args) throws InterruptedException {
        String clientName = args.length > 0 ? args[0] : "error-test-client";
        
        ErrorTestClient client = new ErrorTestClient("localhost", 9090, clientName);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                client.shutdown();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }));
        
        System.out.println("=== gRPC客户端错误处理测试 ===");
        
        // 测试1：正常发送
        client.testNormalSend();
        
        Thread.sleep(2000);
        
        // 测试2：队列满测试
        client.testQueueFull();
        
        Thread.sleep(2000);
        
        // 测试3：持续错误测试
        client.startContinuousErrorTest();
        
        // 保持运行
        Thread.currentThread().join();
    }
}
