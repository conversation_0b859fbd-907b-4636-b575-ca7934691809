package com.example.grpc.server;

import io.grpc.Server;
import io.grpc.ServerBuilder;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * gRPC日志服务器
 */
public class LogServer {
    private static final int PORT = 9090;
    private Server server;

    public void start() throws IOException {
        // 创建日志服务实现
        LogServiceImpl logService = new LogServiceImpl();
        
        // 构建并启动服务器
        server = ServerBuilder.forPort(PORT)
                .addService(logService)
                .build()
                .start();
        
        System.out.println("gRPC日志服务器已启动，监听端口: " + PORT);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.err.println("*** 正在关闭gRPC服务器，因为JVM正在关闭");
            try {
                LogServer.this.stop();
            } catch (InterruptedException e) {
                e.printStackTrace(System.err);
            }
            System.err.println("*** 服务器已关闭");
        }));
    }

    public void stop() throws InterruptedException {
        if (server != null) {
            server.shutdown().awaitTermination(30, TimeUnit.SECONDS);
        }
    }

    /**
     * 等待服务器终止
     */
    public void blockUntilShutdown() throws InterruptedException {
        if (server != null) {
            server.awaitTermination();
        }
    }

    public static void main(String[] args) throws IOException, InterruptedException {
        final LogServer server = new LogServer();
        server.start();
        server.blockUntilShutdown();
    }
}
