package com.example.grpc.server;

import com.example.grpc.proto.LogBean;
import com.example.grpc.proto.LogResponse;
import com.example.grpc.proto.LogServiceGrpc;
import io.grpc.stub.StreamObserver;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 日志服务实现类
 */
public class LogServiceImpl extends LogServiceGrpc.LogServiceImplBase {
    
    // 容量为1000的阻塞队列
    private final BlockingQueue<LogBean> logQueue = new ArrayBlockingQueue<>(1000);
    
    // 线程池用于异步处理日志
    private final ExecutorService consumerExecutor = Executors.newFixedThreadPool(3);
    
    // 日期格式化器
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public LogServiceImpl() {
        // 启动消费者线程
        startConsumerThreads();
    }

    @Override
    public void sendLog(LogBean request, StreamObserver<LogResponse> responseObserver) {
        try {
            // 尝试将日志放入队列
            boolean success = logQueue.offer(request);
            
            LogResponse.Builder responseBuilder = LogResponse.newBuilder();
            
            if (success) {
                // 成功放入队列
                responseBuilder.setSuccess(true);
            } else {
                // 队列已满
                responseBuilder.setSuccess(false)
                              .setErrorMessage("日志队列已满，请稍后重试");
            }
            
            LogResponse response = responseBuilder.build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            
        } catch (Exception e) {
            // 处理异常
            LogResponse response = LogResponse.newBuilder()
                    .setSuccess(false)
                    .setErrorMessage("服务器内部错误: " + e.getMessage())
                    .build();
            
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * 启动多个消费者线程异步处理日志
     */
    private void startConsumerThreads() {
        // 启动3个消费者线程
        for (int i = 0; i < 3; i++) {
            final int threadId = i + 1;
            consumerExecutor.submit(new LogConsumer(threadId));
        }
    }

    /**
     * 日志消费者线程
     */
    private class LogConsumer implements Runnable {
        private final int threadId;

        public LogConsumer(int threadId) {
            this.threadId = threadId;
        }

        @Override
        public void run() {
            System.out.println("日志消费者线程-" + threadId + " 已启动");
            
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // 从队列中取出日志事件（阻塞等待）
                    LogBean logBean = logQueue.take();
                    
                    // 模拟处理日志（打印到控制台）
                    processLog(logBean);
                    
                } catch (InterruptedException e) {
                    // 线程被中断，退出循环
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    System.err.println("消费者线程-" + threadId + " 处理日志时发生错误: " + e.getMessage());
                }
            }
            
            System.out.println("日志消费者线程-" + threadId + " 已停止");
        }

        /**
         * 处理日志事件
         */
        private void processLog(LogBean logBean) {
            // 格式化时间戳
            String formattedTime = dateFormat.format(new Date(logBean.getTimestamp()));
            
            // 格式化日志级别
            String level = logBean.getLevel().name();
            
            // 按照要求的格式输出日志
            System.out.println(String.format("[%s] %s %s - %s", 
                    level, 
                    logBean.getClientName(), 
                    formattedTime, 
                    logBean.getMessage()));
        }
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        consumerExecutor.shutdown();
    }
}
