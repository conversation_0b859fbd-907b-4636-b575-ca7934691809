# gRPC分布式日志记录系统

本题要求模拟实现 一个简化的远程日志记录系统， 客户端通过 gRPC 将日志事件发送给远程服务端，服务端接收并异步 处理日志记录。
## 项目结构

```
├── pom.xml                                    # Maven配置文件
├── src/main/proto/log_service.proto          # gRPC proto文件定义
├── src/main/java/com/example/grpc/
│   ├── server/
│   │   ├── LogServer.java                     # gRPC服务器
│   │   └── LogServiceImpl.java               # 日志服务实现（异步队列处理）
│   └── client/
│       ├── LogClient.java                     # gRPC客户端（定时发送）
│       └── MultiClientLauncher.java          # 多客户端启动器
├── start-server.sh                           # 服务器启动脚本
└── start-clients.sh                          # 客户端启动脚本
```

## 功能实现

1. **gRPC Proto定义**：
   - LogBean消息：包含日志级别、时间戳、消息内容、客户端名称
   - LogService服务：提供sendLog方法
   - LogResponse响应：返回成功状态和错误信息

2. **服务端功能**：
   - 接收gRPC日志请求
   - 使用容量为1000的阻塞队列缓存日志
   - 启动3个后台消费者线程异步处理日志
   - 队列满时返回错误响应

3. **客户端功能**：
   - 使用ScheduledExecutorService每秒发送日志
   - 支持多客户端同时运行
   - 自动处理服务端错误响应

## 运行说明

### 快速演示
```bash
# 一键演示（推荐）
./demo.sh
```

### 手动运行

#### 1. 编译项目
```bash
mvn clean compile
```

#### 2. 启动服务端
```bash
# 方式1：使用脚本
./start-server.sh

# 方式2：直接运行
mvn exec:java -Dexec.mainClass="com.example.grpc.server.LogServer"
```

#### 3. 启动客户端
```bash
# 方式1：启动多个客户端
./start-clients.sh

# 方式2：启动单个客户端
mvn exec:java -Dexec.mainClass="com.example.grpc.client.LogClient" -Dexec.args="client-A"

# 方式3：压力测试（测试队列满的情况）
mvn exec:java -Dexec.mainClass="com.example.grpc.client.StressTestClient" -Dexec.args="stress-client 2000"
```

## 预期输出

### 服务端控制台输出示例：
```
gRPC日志服务器已启动，监听端口: 9090
日志消费者线程-1 已启动
日志消费者线程-2 已启动
日志消费者线程-3 已启动
[INFO] client-A 2025-04-29 10:00:00 - 应用启动成功。
[WARN] client-B 2025-04-29 10:00:01 - 配置文件缺失，使用默认值。
[ERROR] client-A 2025-04-29 10:00:02 - 数据库连接失败：TimeoutException
[INFO] client-C 2025-04-29 10:00:03 - 用户登录成功。
```

### 客户端控制台输出示例：
```
客户端 client-A 开始定时发送日志...
客户端 client-A 成功发送日志: INFO - 应用启动成功。
客户端 client-A 成功发送日志: WARN - 内存使用率较高。
```

## 环境要求

- Java 8+
- Maven 3.6+
- 网络端口9090可用
- 要求已经配置java和mavn到系统环境变量
