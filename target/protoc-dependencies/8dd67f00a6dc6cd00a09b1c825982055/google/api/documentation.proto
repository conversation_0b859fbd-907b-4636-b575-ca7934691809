// Copyright 2015 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.api;

option go_package = "google.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig";
option java_multiple_files = true;
option java_outer_classname = "DocumentationProto";
option java_package = "com.google.api";
option objc_class_prefix = "GAPI";

// `Documentation` provides the information for describing a service.
//
// Example:
// <pre><code>documentation:
//   summary: >
//     The Google Calendar API gives access
//     to most calendar features.
//   pages:
//   - name: Overview
//     content: &#40;== include google/foo/overview.md ==&#41;
//   - name: Tutorial
//     content: &#40;== include google/foo/tutorial.md ==&#41;
//     subpages;
//     - name: Java
//       content: &#40;== include google/foo/tutorial_java.md ==&#41;
//   rules:
//   - selector: google.calendar.Calendar.Get
//     description: >
//       ...
//   - selector: google.calendar.Calendar.Put
//     description: >
//       ...
// </code></pre>
// Documentation is provided in markdown syntax. In addition to
// standard markdown features, definition lists, tables and fenced
// code blocks are supported. Section headers can be provided and are
// interpreted relative to the section nesting of the context where
// a documentation fragment is embedded.
//
// Documentation from the IDL is merged with documentation defined
// via the config at normalization time, where documentation provided
// by config rules overrides IDL provided.
//
// A number of constructs specific to the API platform are supported
// in documentation text.
//
// In order to reference a proto element, the following
// notation can be used:
// <pre><code>&#91;fully.qualified.proto.name]&#91;]</code></pre>
// To override the display text used for the link, this can be used:
// <pre><code>&#91;display text]&#91;fully.qualified.proto.name]</code></pre>
// Text can be excluded from doc using the following notation:
// <pre><code>&#40;-- internal comment --&#41;</code></pre>
//
// A few directives are available in documentation. Note that
// directives must appear on a single line to be properly
// identified. The `include` directive includes a markdown file from
// an external source:
// <pre><code>&#40;== include path/to/file ==&#41;</code></pre>
// The `resource_for` directive marks a message to be the resource of
// a collection in REST view. If it is not specified, tools attempt
// to infer the resource from the operations in a collection:
// <pre><code>&#40;== resource_for v1.shelves.books ==&#41;</code></pre>
// The directive `suppress_warning` does not directly affect documentation
// and is documented together with service config validation.
message Documentation {
  // A short summary of what the service does. Can only be provided by
  // plain text.
  string summary = 1;

  // The top level pages for the documentation set.
  repeated Page pages = 5;

  // A list of documentation rules that apply to individual API elements.
  //
  // **NOTE:** All service configuration rules follow "last one wins" order.
  repeated DocumentationRule rules = 3;

  // The URL to the root of documentation.
  string documentation_root_url = 4;

  // Specifies the service root url if the default one (the service name
  // from the yaml file) is not suitable. This can be seen in any fully
  // specified service urls as well as sections that show a base that other
  // urls are relative to.
  string service_root_url = 6;

  // Declares a single overview page. For example:
  // <pre><code>documentation:
  //   summary: ...
  //   overview: &#40;== include overview.md ==&#41;
  // </code></pre>
  // This is a shortcut for the following declaration (using pages style):
  // <pre><code>documentation:
  //   summary: ...
  //   pages:
  //   - name: Overview
  //     content: &#40;== include overview.md ==&#41;
  // </code></pre>
  // Note: you cannot specify both `overview` field and `pages` field.
  string overview = 2;
}

// A documentation rule provides information about individual API elements.
message DocumentationRule {
  // The selector is a comma-separated list of patterns. Each pattern is a
  // qualified name of the element which may end in "*", indicating a wildcard.
  // Wildcards are only allowed at the end and for a whole component of the
  // qualified name, i.e. "foo.*" is ok, but not "foo.b*" or "foo.*.bar". A
  // wildcard will match one or more components. To specify a default for all
  // applicable elements, the whole pattern "*" is used.
  string selector = 1;

  // Description of the selected API(s).
  string description = 2;

  // Deprecation description of the selected element(s). It can be provided if
  // an element is marked as `deprecated`.
  string deprecation_description = 3;
}

// Represents a documentation page. A page can contain subpages to represent
// nested documentation set structure.
message Page {
  // The name of the page. It will be used as an identity of the page to
  // generate URI of the page, text of the link to this page in navigation,
  // etc. The full page name (start from the root page name to this page
  // concatenated with `.`) can be used as reference to the page in your
  // documentation. For example:
  // <pre><code>pages:
  // - name: Tutorial
  //   content: &#40;== include tutorial.md ==&#41;
  //   subpages:
  //   - name: Java
  //     content: &#40;== include tutorial_java.md ==&#41;
  // </code></pre>
  // You can reference `Java` page using Markdown reference link syntax:
  // `[Java][Tutorial.Java]`.
  string name = 1;

  // The Markdown content of the page. You can use <code>&#40;== include {path}
  // ==&#41;</code> to include content from a Markdown file.
  string content = 2;

  // Subpages of this page. The order of subpages specified here will be
  // honored in the generated docset.
  repeated Page subpages = 3;
}
