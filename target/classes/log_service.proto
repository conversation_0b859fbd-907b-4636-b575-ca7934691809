syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.example.grpc.proto";
option java_outer_classname = "LogServiceProto";

package logservice;

// 日志级别枚举
enum LogLevel {
  DEBUG = 0;
  INFO = 1;
  WARN = 2;
  ERROR = 3;
}

// 日志消息定义
message LogBean {
  LogLevel level = 1;        // 日志级别
  int64 timestamp = 2;       // 时间戳（long类型）
  string message = 3;        // 日志消息内容
  string client_name = 4;    // 发送者客户端名称
}

// 日志响应定义
message LogResponse {
  bool success = 1;          // 是否成功
  string error_message = 2;  // 错误消息（如果有）
}

// 日志服务定义
service LogService {
  // 发送日志方法
  rpc sendLog(LogBean) returns (LogResponse);
}
