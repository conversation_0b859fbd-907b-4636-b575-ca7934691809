// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: log_service.proto

package com.example.grpc.proto;

/**
 * <pre>
 * 日志级别枚举
 * </pre>
 *
 * Protobuf enum {@code logservice.LogLevel}
 */
public enum LogLevel
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>DEBUG = 0;</code>
   */
  DEBUG(0),
  /**
   * <code>INFO = 1;</code>
   */
  INFO(1),
  /**
   * <code>WARN = 2;</code>
   */
  WARN(2),
  /**
   * <code>ERROR = 3;</code>
   */
  ERROR(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>DEBUG = 0;</code>
   */
  public static final int DEBUG_VALUE = 0;
  /**
   * <code>INFO = 1;</code>
   */
  public static final int INFO_VALUE = 1;
  /**
   * <code>WARN = 2;</code>
   */
  public static final int WARN_VALUE = 2;
  /**
   * <code>ERROR = 3;</code>
   */
  public static final int ERROR_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static LogLevel valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static LogLevel forNumber(int value) {
    switch (value) {
      case 0: return DEBUG;
      case 1: return INFO;
      case 2: return WARN;
      case 3: return ERROR;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<LogLevel>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      LogLevel> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<LogLevel>() {
          public LogLevel findValueByNumber(int number) {
            return LogLevel.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.example.grpc.proto.LogServiceProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final LogLevel[] VALUES = values();

  public static LogLevel valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private LogLevel(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:logservice.LogLevel)
}

