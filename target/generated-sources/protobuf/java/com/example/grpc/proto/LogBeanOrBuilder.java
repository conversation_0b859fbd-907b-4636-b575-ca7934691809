// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: log_service.proto

package com.example.grpc.proto;

public interface LogBeanOrBuilder extends
    // @@protoc_insertion_point(interface_extends:logservice.LogBean)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 日志级别
   * </pre>
   *
   * <code>.logservice.LogLevel level = 1;</code>
   * @return The enum numeric value on the wire for level.
   */
  int getLevelValue();
  /**
   * <pre>
   * 日志级别
   * </pre>
   *
   * <code>.logservice.LogLevel level = 1;</code>
   * @return The level.
   */
  com.example.grpc.proto.LogLevel getLevel();

  /**
   * <pre>
   * 时间戳（long类型）
   * </pre>
   *
   * <code>int64 timestamp = 2;</code>
   * @return The timestamp.
   */
  long getTimestamp();

  /**
   * <pre>
   * 日志消息内容
   * </pre>
   *
   * <code>string message = 3;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <pre>
   * 日志消息内容
   * </pre>
   *
   * <code>string message = 3;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <pre>
   * 发送者客户端名称
   * </pre>
   *
   * <code>string client_name = 4;</code>
   * @return The clientName.
   */
  java.lang.String getClientName();
  /**
   * <pre>
   * 发送者客户端名称
   * </pre>
   *
   * <code>string client_name = 4;</code>
   * @return The bytes for clientName.
   */
  com.google.protobuf.ByteString
      getClientNameBytes();
}
