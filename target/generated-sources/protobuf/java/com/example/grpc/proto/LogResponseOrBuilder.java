// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: log_service.proto

package com.example.grpc.proto;

public interface LogResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:logservice.LogResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 是否成功
   * </pre>
   *
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <pre>
   * 错误消息（如果有）
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * 错误消息（如果有）
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();
}
