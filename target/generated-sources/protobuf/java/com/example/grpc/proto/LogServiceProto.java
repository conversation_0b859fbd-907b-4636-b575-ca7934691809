// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: log_service.proto

package com.example.grpc.proto;

public final class LogServiceProto {
  private LogServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_logservice_LogBean_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_logservice_LogBean_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_logservice_LogResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_logservice_LogResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021log_service.proto\022\nlogservice\"g\n\007LogBe" +
      "an\022#\n\005level\030\001 \001(\0162\024.logservice.LogLevel\022" +
      "\021\n\ttimestamp\030\002 \001(\003\022\017\n\007message\030\003 \001(\t\022\023\n\013c" +
      "lient_name\030\004 \001(\t\"5\n\013LogResponse\022\017\n\007succe" +
      "ss\030\001 \001(\010\022\025\n\rerror_message\030\002 \001(\t*4\n\010LogLe" +
      "vel\022\t\n\005DEBUG\020\000\022\010\n\004INFO\020\001\022\010\n\004WARN\020\002\022\t\n\005ER" +
      "ROR\020\0032E\n\nLogService\0227\n\007sendLog\022\023.logserv" +
      "ice.LogBean\032\027.logservice.LogResponseB+\n\026" +
      "com.example.grpc.protoB\017LogServiceProtoP" +
      "\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_logservice_LogBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_logservice_LogBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_logservice_LogBean_descriptor,
        new java.lang.String[] { "Level", "Timestamp", "Message", "ClientName", });
    internal_static_logservice_LogResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_logservice_LogResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_logservice_LogResponse_descriptor,
        new java.lang.String[] { "Success", "ErrorMessage", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
