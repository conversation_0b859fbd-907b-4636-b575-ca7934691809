@echo off
echo 启动gRPC日志服务器...
echo.

REM 检查Maven是否可用
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Maven，请确保Maven已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

echo 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 启动服务器（监听端口9090）...
echo 按 Ctrl+C 停止服务器
echo.

mvn exec:java -Dexec.mainClass="com.example.grpc.server.LogServer"

pause
